<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
    v-bind="$attrs"
  >
    <div
      class="flex flex-row items-center"
      :class="{ 'px-3 py-1': mini, 'p-3': !mini }"
    >
      <UAvatar
        :icon="voice.icon"
        size="xl"
        :class="{
          innertape2: isPlaying
        }"
      />
      <div class="text-xs">
        {{ props.voice.speaker_name }}
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

interface VoiceCardProps {
  voice: SpeechVoice
  isPlaying?: boolean
  mini?: boolean
}

const props = defineProps<VoiceCardProps>()

const { getAccentByValue } = useSpeechVoices()
</script>
